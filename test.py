"""
优化的对话式测试工具
支持单次测试和交互式对话模式
"""

import requests
import json
import argparse
import time


class CozMealsChat:
    def __init__(self, device_sn="testDevice001", session_id=None):
        self.url = "http://127.0.0.1:8002/cal-server/ai/agents/coz_meals_steam"
        self.device_sn = device_sn
        self.session_id = session_id or f"testSession_{int(time.time())}"
        self.headers = {
            "Content-Type": "application/json",
            "timeZoneId": "Asia/Shanghai",
            "deviceSn": self.device_sn,
            "sessionid": self.session_id
        }

    def print_meal_suggestions(self, meals):
        """美化输出菜品推荐JSON数据"""
        print("\n🍽️ 菜品推荐：")
        print("=" * 50)

        if not meals:
            print("暂无推荐菜品")
            return

        # 打印格式化的JSON
        print("[")
        for i, meal in enumerate(meals):
            is_last = i == len(meals) - 1
            comma = "" if is_last else ","

            meal_name = meal.get("mealName", "未知菜品")
            meal_uid = meal.get("mealUid", "")
            img_url = meal.get("img", "")

            print(f'    {{')
            print(f'        "mealName": "{meal_name}",')
            print(f'        "mealUid": "{meal_uid}",')
            print(f'        "img": "{img_url}"')
            print(f'    }}{comma}')
        print("]")
        print("=" * 50)

    def send_message(self, message):
        """发送消息并处理流式响应"""
        data = {"message": message}

        try:
            response = requests.post(self.url, json=data, headers=self.headers, stream=True)
            if response.status_code != 200:
                print(f"❌ 请求失败: {response.status_code} - {response.text}")
                return

            # 处理流式响应
            buffer = b""
            ai_response = ""

            for chunk in response.iter_content(chunk_size=1):
                if not chunk:
                    continue
                buffer += chunk
                while b"\n" in buffer:
                    line, buffer = buffer.split(b"\n", 1)
                    if not line:
                        continue
                    try:
                        data_json = line.decode("utf-8").strip()
                        if not data_json.startswith("data: "):
                            continue
                        payload = data_json[len("data: "):]
                        obj = json.loads(payload)

                        # 获取响应内容
                        response_classify = obj.get("responseDataClassify", "")
                        content = obj.get("responseData", "")

                        if content:
                            # 检查是否为JSON格式的菜品推荐数据
                            if response_classify == "food_suggestion" and isinstance(content, list):
                                self.print_meal_suggestions(content)
                            else:
                                print(content, end="", flush=True)
                                ai_response += content

                    except Exception as e:
                        # 静默处理解析错误，避免干扰对话流
                        pass

            print()  # 换行
            return ai_response

        except requests.exceptions.ConnectionError:
            print("❌ 连接失败，请确保服务器正在运行")
        except Exception as e:
            print(f"❌ 发生错误: {e}")

    def interactive_mode(self):
        """交互式对话模式"""
        print("🍽️  Coz 饮食助手 - 交互模式")
        print("=" * 50)
        print("💡 提示：输入 'quit' 或 'exit' 退出对话")
        print("💡 提示：输入 'clear' 清屏")
        print("💡 提示：输入 'help' 查看帮助")
        print("-" * 50)

        while True:
            try:
                # 用户输入
                user_input = input("\n👤 用户：").strip()

                if not user_input:
                    continue

                # 处理特殊命令
                if user_input.lower() in ['quit', 'exit', 'q']:
                    print("\n👋 再见！")
                    break
                elif user_input.lower() == 'clear':
                    import os
                    os.system('clear' if os.name == 'posix' else 'cls')
                    continue
                elif user_input.lower() == 'help':
                    self.show_help()
                    continue

                # 发送消息
                print("🤖 Cozy：", end="")
                self.send_message(user_input)

            except KeyboardInterrupt:
                print("\n\n👋 再见！")
                break
            except EOFError:
                print("\n\n👋 再见！")
                break

    def show_help(self):
        """显示帮助信息"""
        help_text = """
📖 帮助信息：

🍽️  饮食安排示例：
   • "我想吃鸡腿"
   • "今天午餐安排牛肉面"
   • "明天早餐吃面包和牛奶"

🔍 菜品推荐示例：
   • "给我推荐一些菜品"
   • "推荐几个午餐"
   • "有什么好吃的建议"

📋 查询示例：
   • "目前我的菜单有什么"
   • "今天的安排是什么"

⚙️  特殊命令：
   • quit/exit/q - 退出程序
   • clear - 清屏
   • help - 显示此帮助
        """
        print(help_text)


def main():
    parser = argparse.ArgumentParser(description="Coz 饮食助手测试工具")
    parser.add_argument('--msg', type=str, help='发送单条消息')
    parser.add_argument('--device', type=str, default='testDevice001', help='设备序列号')
    parser.add_argument('--session', type=str, help='会话ID（默认自动生成）')
    parser.add_argument('--interactive', '-i', action='store_true', help='启动交互模式')

    args = parser.parse_args()

    # 创建聊天实例
    chat = CozMealsChat(device_sn=args.device, session_id=args.session)

    if args.interactive or not args.msg:
        # 交互模式
        chat.interactive_mode()
    else:
        # 单次消息模式
        print(f"👤 用户：{args.msg}")
        print("🤖 Cozy：", end="")
        chat.send_message(args.msg)


if __name__ == "__main__":
    main()
