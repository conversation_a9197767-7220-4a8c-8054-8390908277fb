from typing import TypedDict

import datetime
import json
from typing import Annotated, TypedDict, List
from langchain_core.messages import BaseMessage, SystemMessage
import operator
from langgraph.graph import END, StateGraph
from langgraph.prebuilt import ToolNode
from pydantic import BaseModel
from coz.conversation_store import session_states, SessionStateType, CurrentMealType

from langchain_core.tools import tool

# from logger import logger

from coz.cozllm import coz_llm_model

llm = coz_llm_model()

# Agent的系统提示词配置
MEALS_SYSTEM_PROMPT = open("coz/prompt.md", "r").read()


class MealNameType(TypedDict):
    mealName: str
    mealUid: str
    img: str


class CozMealsGraphState(TypedDict):
    messages: Annotated[List[BaseMessage], operator.add]
    error_msg: Annotated[str, operator.add]  # 用于存储错误信息
    tool_call: Annotated[bool, operator.add]
    tool_call_params: Annotated[BaseModel, operator.add]  # 用于标记是否需要调用工具

    # 饮食相关状态
    # intendMeal: str
    # selectedMealNames: list
    # existsMealName: list

    mealKind: str
    mealDate: str
    mealNames: list[MealNameType]
    meal_dict: dict
    current_meal: dict  # 当前饮食日程信息

    # 会话上下文
    deviceSn: str
    session_id: str
    timezone: str
    laster_tool_call: str


def format_prompt(prompt: str, json_output=False, **kwargs):
    """格式化提示词模板，替换占位符"""
    prompt2 = prompt
    for k, v in kwargs.items():
        if json_output:
            prompt2 = prompt2.replace(f"<{k}>\n</{k}>", f"<{k}>\n{json.dumps(v,ensure_ascii=False,indent=2)}\n</{k}>")
        else:
            prompt2 = prompt2.replace(f"<{k}>", str(v))
    return prompt2


def create_system_message(current_meal: dict, deviceSn: str, session_id: str, timezone: str, recommended_meal_names: list = None) -> SystemMessage:
    """创建包含上下文信息的系统消息"""
    # 获取当前时间
    now = datetime.datetime.now()
    current_time_str = now.strftime("%Y-%m-%d %H:%M:%S %Z")

    # 如果没有传入推荐菜品列表，则设为空列表
    if recommended_meal_names is None:
        recommended_meal_names = []

    # 格式化上下文提示词
    system_prompt = format_prompt(
        MEALS_SYSTEM_PROMPT,
        current_time_str=current_time_str,
        session_id=session_id,
        timezone=timezone,
        deviceSn=deviceSn,
        currentMealDate=current_meal.get("mealDate", ""),
        currentMealKind=current_meal.get("mealKind", ""),
        currentMealNames=current_meal.get("mealNames", []),
        recommendedMealNames=recommended_meal_names,
    )

    return SystemMessage(content=system_prompt)


class SessionManager:
    """会话管理器 - 负责管理单个会话的状态"""

    def __init__(self, deviceSn: str, session_id: str, timezone: str):
        self.deviceSn = deviceSn
        self.session_id = session_id
        self.timezone = timezone
        self.session_key = self._get_session_key()

        # 加载或初始化会话状态
        self.session_state = self._load_session_state()

    def _get_session_key(self) -> str:
        """生成会话键"""
        return f"{self.deviceSn}_meals_{self.session_id}"

    def _load_session_state(self) -> SessionStateType:
        """加载会话状态，如果不存在则创建新的会话状态"""
        # 如果会话状态不存在，创建新的空状态
        if self.session_key not in session_states:
            # 创建新的会话状态，不再依赖旧的全局存储
            return {
                "current_meal": {"mealDate": "", "mealKind": "", "mealNames": []},
                "meal_dict": {},  # 空的菜品字典
                "last_tool_call": "",
                "messages": [],  # 空的消息历史
                "selected_meal_names": [],  # 空的选择菜品列表
                "tool_history": [],  # 空的工具调用历史
                "recommendedMealNames": []  # AI推荐过的所有菜品名称列表
            }

        # 确保返回的状态包含所有必要的字段
        state = session_states[self.session_key]
        default_fields = {
            "messages": [],
            "current_meal": {"mealDate": "", "mealKind": "", "mealNames": []},
            "meal_dict": {},
            "last_tool_call": "",
            "selected_meal_names": [],
            "tool_history": [],
            "recommendedMealNames": []  # AI推荐过的所有菜品名称列表
        }

        for field, default_value in default_fields.items():
            if field not in state:
                state[field] = default_value

        return state

    def get_current_meal(self) -> CurrentMealType:
        """获取当前饮食信息"""
        return self.session_state.get("current_meal", {"mealDate": "", "mealKind": "", "mealNames": []})

    def update_current_meal(self, meal_data: CurrentMealType):
        """更新当前饮食信息"""
        self.session_state["current_meal"] = meal_data

    def get_meal_date(self) -> str:
        """获取当前饮食日期"""
        return self.get_current_meal().get("mealDate", "")

    def get_meal_kind(self) -> str:
        """获取当前饮食类型"""
        return self.get_current_meal().get("mealKind", "")

    def get_meal_names(self) -> list:
        """获取当前饮食菜品列表"""
        return self.get_current_meal().get("mealNames", [])

    def get_meal_dict(self) -> dict:
        """获取菜品详细信息"""
        return self.session_state.get("meal_dict", {})

    def update_meal_dict(self, meal_dict: dict):
        """更新菜品详细信息"""
        self.session_state["meal_dict"] = meal_dict

    def get_messages(self) -> List[BaseMessage]:
        """获取会话消息历史"""
        return self.session_state.get("messages", [])

    def add_message(self, message: BaseMessage):
        """添加单条消息到会话历史"""
        if "messages" not in self.session_state:
            self.session_state["messages"] = []
        self.session_state["messages"].append(message)

    def add_messages(self, messages: List[BaseMessage]):
        """添加多条消息到会话历史"""
        if "messages" not in self.session_state:
            self.session_state["messages"] = []
        self.session_state["messages"].extend(messages)

    def clear_messages(self):
        """清空会话消息历史"""
        self.session_state["messages"] = []

    def get_selected_meal_names(self) -> list:
        """获取用户选择的菜品名称"""
        return self.session_state.get("selected_meal_names", [])

    def set_selected_meal_names(self, meal_names: list):
        """设置用户选择的菜品名称"""
        self.session_state["selected_meal_names"] = meal_names

    def get_tool_history(self) -> list:
        """获取工具调用历史"""
        return self.session_state.get("tool_history", [])

    def add_tool_call(self, tool_name: str):
        """添加工具调用记录"""
        if "tool_history" not in self.session_state:
            self.session_state["tool_history"] = []
        self.session_state["tool_history"].append(tool_name)

    def clear_tool_history(self):
        """清空工具调用历史"""
        self.session_state["tool_history"] = []

    def get_recommended_meal_names(self) -> list:
        """获取AI推荐过的菜品名称列表"""
        return self.session_state.get("recommendedMealNames", [])

    def add_recommended_meal_names(self, meal_names: list):
        """添加AI推荐的菜品名称到列表中，自动去重"""
        if "recommendedMealNames" not in self.session_state:
            self.session_state["recommendedMealNames"] = []

        current_recommended = set(self.session_state["recommendedMealNames"])

        # 添加新的菜品名称，去重
        for meal_name in meal_names:
            if meal_name and meal_name not in current_recommended:
                self.session_state["recommendedMealNames"].append(meal_name)
                current_recommended.add(meal_name)

    def clear_recommended_meal_names(self):
        """清空AI推荐过的菜品名称列表"""
        self.session_state["recommendedMealNames"] = []

    def save_state(self):
        """保存会话状态"""
        session_states[self.session_key] = self.session_state

    def create_graph_state(self, additional_messages: List[BaseMessage] = None) -> CozMealsGraphState:
        """创建 graph_state"""
        # 获取会话中的消息历史
        messages = self.get_messages().copy()

        # 如果有额外的消息，添加到消息列表中
        if additional_messages:
            messages.extend(additional_messages)

        current_meal: CurrentMealType = self.get_current_meal()

        return {
            "messages": messages,
            "error_msg": "",
            "tool_call": False,
            "tool_call_params": None,
            # "intendMeal": "",
            # "selectedMealNames": [],
            # "existsMealName": [],
            "mealKind": current_meal.get("mealKind", ""),
            "mealDate": current_meal.get("mealDate", ""),
            "mealNames": [],
            "meal_dict": self.get_meal_dict(),
            "current_meal": current_meal,
            "deviceSn": self.deviceSn,
            "session_id": self.session_id,
            "timezone": self.timezone,
            "laster_tool_call": self.session_state.get("last_tool_call", "")
        }




# @tool(name_or_callable="get_food_suggestion",
#       description="Search for existing meals based on user's intended meal name. Use this when user mentions a specific meal they want to arrange.")
def get_food_suggestion(foodName: str, mealKind: str, mealDate: str, deviceSn: str, session_id: str):
    """Search for existing meals based on user intent.
    Args:
            foodName: str, the meal name or food the user wants to arrange
            deviceSn: The current deviceSn,
            session_id: str,
            timezone: str
    """

    # logger.info(f"get_food_suggestion tool called with userIntent: {foodName}")
    # 使用 SessionManager 记录工具调用
    try:
        session_manager = SessionManager(deviceSn, session_id, "UTC")
        session_manager.add_tool_call("get_food_suggestion")
        session_manager.save_state()
    except Exception as e:
        # 如果 SessionManager 不可用，跳过记录
        pass

    # 模拟搜索逻辑 - 这里可以连接实际的数据库或API
    # 处理鸡肉相关菜品
    if "鸡" in foodName:
        existing_meals: list[MealNameType] = [
            {"mealName": "宫保鸡丁", "mealUid": "chicken001", "img": "https://example.com/gongbao.jpg"},
            {"mealName": "黄焖鸡", "mealUid": "chicken002", "img": "https://example.com/huangmen.jpg"},
            {"mealName": "辣子鸡", "mealUid": "chicken003", "img": "https://example.com/lazi.jpg"},
            {"mealName": "椰香咖喱鸡", "mealUid": "chicken004", "img": "https://example.com/curry.jpg"},
            {"mealName": "烤鸡", "mealUid": "chicken005", "img": "https://example.com/roast.jpg"},
        ]
    elif "牛" in foodName:
        existing_meals: list[MealNameType] = [
            {"mealName": "兰州牛肉面", "mealUid": "beef001", "img": "https://example.com/lanzhou.jpg"},
            {"mealName": "红烧牛肉", "mealUid": "beef002", "img": "https://example.com/hongshao.jpg"},
        ]
    elif "猪" in foodName:
        existing_meals: list[MealNameType] = [
            {"mealName": "红烧肉", "mealUid": "pork001", "img": "https://example.com/hongshaorou.jpg"},
            {"mealName": "糖醋里脊", "mealUid": "pork002", "img": "https://example.com/tangcu.jpg"},
        ]
    elif "羊" in foodName:
        existing_meals: list[MealNameType] = [
            {"mealName": "羊肉串", "mealUid": "lamb001", "img": "https://example.com/yangrou.jpg"},
        ]
    # 处理具体的菜品名称
    elif foodName in ["宫保鸡丁", "黄焖鸡", "辣子鸡", "椰香咖喱鸡", "烤鸡", "柠檬鸡", "口水鸡", "白切鸡", "可乐鸡翅"]:
        # 如果直接是具体菜品名，返回对应的详细信息
        meal_mapping = {
            "宫保鸡丁": {"mealName": "宫保鸡丁", "mealUid": "chicken001", "img": "https://example.com/gongbao.jpg"},
            "黄焖鸡": {"mealName": "黄焖鸡", "mealUid": "chicken002", "img": "https://example.com/huangmen.jpg"},
            "辣子鸡": {"mealName": "辣子鸡", "mealUid": "chicken003", "img": "https://example.com/lazi.jpg"},
            "椰香咖喱鸡": {"mealName": "椰香咖喱鸡", "mealUid": "chicken004", "img": "https://example.com/curry.jpg"},
            "烤鸡": {"mealName": "烤鸡", "mealUid": "chicken005", "img": "https://example.com/roast.jpg"},
            "柠檬鸡": {"mealName": "柠檬鸡", "mealUid": "chicken006", "img": "https://example.com/lemon.jpg"},
            "口水鸡": {"mealName": "口水鸡", "mealUid": "chicken007", "img": "https://example.com/koushui.jpg"},
            "白切鸡": {"mealName": "白切鸡", "mealUid": "chicken008", "img": "https://example.com/baiqie.jpg"},
            "可乐鸡翅": {"mealName": "可乐鸡翅", "mealUid": "chicken009", "img": "https://example.com/cola.jpg"},
        }
        existing_meals: list[MealNameType] = [meal_mapping[foodName]]
    else:
        # 默认菜品
        existing_meals: list[MealNameType] = [
            {"mealName": "Tomato pizza", "mealUid": "meal12345", "img": "https:"},
            {"mealName": "beef pizza", "mealUid": "meal67890", "img": "https:"},
        ]

    result = {"searchQuery": foodName, "existsMealName": existing_meals, "mealKind": mealKind, "mealDate": mealDate, "laster_tool_call": "get_food_suggestion"}

    # Store the tool result message in conversation history using SessionManager
    tool_message = {"type": "tool_result", "tool_name": "get_food_suggestion", "content": result, "timestamp": datetime.datetime.now().isoformat()}

    # 使用 SessionManager 存储工具结果（如果可用）
    try:
        session_manager = SessionManager(deviceSn, session_id, "UTC")
        # 注意：这里我们不直接存储 tool_message，因为 SessionManager 主要管理 BaseMessage 对象
        # 工具结果已经通过返回值传递给调用方
        session_manager.save_state()
    except Exception:
        # 如果 SessionManager 不可用，跳过存储
        pass

    return result


# def get_daily_meals(mealKind: str, mealDate: str, deviceSn: str, session_id: str, timezone: str) -> dict:
#     daily_kind_meals = {
#         "mealDate": mealDate,
#         "mealKind": mealKind,
#         "deviceSn": deviceSn,
#         "session_id": session_id,
#         "timezone": timezone,
#         "meals": [
#             {
#                 "mealName": "Tomato pizza",
#                 "mealUid": "meal12345",
#             },
#             {
#                 "mealName": "beef pizza",
#                 "mealUid": "meal67890",
#             },
#         ],
#     }
#     return daily_kind_meals


# 2. 定义我们的自定义工具
# @tool(name_or_callable="collect_coz_meals_info", description="coz_meals_tools")
# def collect_coz_meals_info(intendMeal: list, mealKind: str, mealDate: str, selectedMealNames, deviceSn: str, session_id: str, timezone: str) -> dict:
#     """check if the chore information is complete.
#     Args:
#             intendMeal: str
#             selectedMealNames: list,
#             mealKind: str,
#             mealDate: str,
#             deviceSn: str,
#             session_id: str,
#             timezone: str
#     """
#     # logger.info("collect_coz_meals_info tool called with parameters:")
#     # 使用 SessionManager 来记录工具调用（如果可用）
#     try:
#         session_manager = SessionManager(deviceSn, session_id, timezone)
#         session_manager.add_tool_call("collect_coz_meals_info")
#         session_manager.save_state()
#     except Exception as e:
#         # 向后兼容：如果 SessionManager 不可用，使用旧的方式
#         from coz.conversation_store import meals_conversation_histories
#         meals_conversation_histories[deviceSn + "_" + session_id + "_meals_tools"] = ["collect_coz_meals_info"]

#     result = {"tool_call": True, "tool_call_params": {"intendMeal": intendMeal, "mealKind": mealKind, "mealDate": mealDate, "deviceSn": deviceSn, "selectedMealNames": selectedMealNames, "title": "Here's a preview of your chore."}}

#     # Store the tool result message in conversation history
#     tool_message = {"type": "tool_result", "tool_name": "collect_coz_meals_info", "content": result, "timestamp": datetime.datetime.now().isoformat()}

#     # Get existing history or create new list
#     history_key = deviceSn + "_" + session_id + "_meals_tools"
#     if history_key not in conversation_histories:
#         conversation_histories[history_key] = []

#     conversation_histories[history_key].append(tool_message)

#     return result


# meals_entity_tools = [collect_coz_meals_info,get_food_suggestion]

# model_with_tools= llm.bind_tools(meals_entity_tools)
model_with_tools = llm

get_food_suggestion_node = ToolNode([get_food_suggestion])

# collect_coz_meals_info_node = ToolNode([collect_coz_meals_info])


# 定义 Agent 节点
def meals_agent_node(state: CozMealsGraphState):
    """
    Agent 节点：调用 LLM 来决定下一步行动
    """
    # logger.info("\n[节点]: Agent 运行中...")

    # 获取消息列表
    messages = state["messages"]

    # 如果没有系统消息，或者需要更新系统消息，则创建新的系统消息
    if not messages or not isinstance(messages[0], SystemMessage):
        # 获取推荐菜品列表
        try:
            session_manager = SessionManager(
                deviceSn=state.get("deviceSn", ""),
                session_id=state.get("session_id", ""),
                timezone=state.get("timezone", "")
            )
            recommended_meal_names = session_manager.get_recommended_meal_names()
        except Exception:
            recommended_meal_names = []

        # 创建包含上下文的系统消息
        system_message = create_system_message(
            current_meal=state.get("current_meal", {}),
            deviceSn=state.get("deviceSn", ""),
            session_id=state.get("session_id", ""),
            timezone=state.get("timezone", ""),
            recommended_meal_names=recommended_meal_names
        )
        # 将系统消息插入到消息列表的开头
        messages = [system_message] + messages

    response = model_with_tools.invoke(messages)
    # 将 LLM 的响应（可能是普通消息，也可能是工具调用请求）添加到状态中
    return {"messages": [response]}


def should_continue(state: CozMealsGraphState) -> str:
    """
    条件边：决定是继续调用工具还是结束流程
    """
    # logger.info("\n state in should_continue: ", state)
    # logger.info("\n[判断]: 检查是否需要调用工具...")
    last_message = state["messages"][-1]
    # Step 1: 检查是否有 tool_call

    # if state["selectedMealNames"]  is not None:
    #    return "meals_agent"

    if hasattr(last_message, "tool_calls") and last_message.tool_calls:
        # print("  - 结论: 是，需要调用工具。", last_message.tool_calls)
        # print(f'last_message.tool_calls>>>>{last_message.tool_calls}')
        tool_calls = last_message.tool_calls
        toolName = tool_calls[0].get("name")
        toolArgs = tool_calls[0].get("args")
        # logger.info("toolArgs>>>",toolArgs)
        # conversation_histories[deviceSn + "_meals_selectedMealNames" + session_id]
        # if "laster_tool_call" in state and state["laster_tool_call"] =='get_meal_exists':
        #     return END
        # if "selectMealNames" in state and state["selectMealNames"] is not None:
        #     return "meals_confirm_node"
        # lasttool = last_message.tool_calls
        return toolName  # 路由到工具节点
    else:
        # logger.info("  - 结论: 否，流程结束。")
        return END  # 结束流程


# 创建 StateGraph 对象，并传入我们定义的状态
workflow = StateGraph(CozMealsGraphState)
# 添加节点
workflow.add_node("meals_agent", meals_agent_node)
workflow.add_node("get_food_suggestion_node", get_food_suggestion_node)

# workflow.add_node("meals_confirm_node", meals_confirm_node)
# 设置入口点
workflow.set_entry_point("meals_agent")
# 添加条件边
workflow.add_conditional_edges(
    "meals_agent",  # 从 agent 节点出发
    should_continue,  # 使用 should_continue 函数做判断
    {
        # "meals_confirm_node":"meals_confirm_node",
        "get_food_suggestion": "get_food_suggestion_node",  # 如果返回 "tools"，则流向 tools 节点
        "meals_agent": "meals_agent",  # 如果返回 "tools"，则流向 tools 节点
        END: END,  # 如果返回 END，则结束
    },
)

# 添加从工具节点回到 Agent 节点的边
# 这样，工具执行完后，结果会返回给 Agent，让它做下一步的总结回复
workflow.add_edge("meals_agent", END)
workflow.add_edge("get_food_suggestion_node", END)


# 编译图，生成可运行的应用
cozMealsAgent = workflow.compile()
# cozMealsAgent.get_graph(xray=True).draw_mermaid_png(output_file_path="cozMealsAgent_v5.png")
