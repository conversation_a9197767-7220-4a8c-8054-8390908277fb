import os
from anthropic import Anthropic
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 设置API凭证（从环境变量或直接设置）
api_key = os.getenv("ANTHROPIC_API_KEY")
if not api_key:
    raise ValueError("请在.env文件中设置ANTHROPIC_API_KEY")

# 验证API密钥格式
if not api_key.startswith("sk-ant-"):
    print("警告：API密钥格式可能不正确")
    print("Anthropic API密钥应该以 'sk-ant-' 开头")
    print("请检查您的 .env 文件中的 ANTHROPIC_API_KEY")
    print("您可以在 https://console.anthropic.com/ 获取正确的API密钥")

# 初始化Anthropic客户端
anthropic = Anthropic(api_key=api_key)

def call_claude(prompt: str, max_tokens: int = 1000) -> str:
    """
    使用Claude模型生成文本

    参数:
        prompt: 用户输入的提示
        max_tokens: 最大生成token数

    返回:
        Claude生成的文本
    """
    try:
        # 使用新版API调用方式
        response = anthropic.messages.create(
            model="claude-3-5-sonnet-20241022",  # 使用最新的Claude 3.5 Sonnet模型
            max_tokens=max_tokens,
            messages=[
                {
                    "role": "user",
                    "content": prompt
                }
            ]
        )

        # 检查响应类型
        if isinstance(response, str):
            # 如果返回的是字符串（通常是HTML错误页面），说明API调用失败
            if "<html>" in response.lower():
                raise Exception("API调用返回了HTML页面，可能是API密钥无效或网络问题")
            return response

        # 返回生成的文本内容
        if hasattr(response, 'content') and len(response.content) > 0:
            return response.content[0].text
        else:
            raise Exception("API响应格式异常")

    except Exception as e:
        error_msg = f"调用Claude API时发生错误: {str(e)}"
        print(error_msg)
        print("\n可能的解决方案:")
        print("1. 检查API密钥是否正确（应该以 'sk-ant-' 开头）")
        print("2. 检查网络连接")
        print("3. 确认API密钥有足够的额度")
        print("4. 访问 https://console.anthropic.com/ 获取正确的API密钥")
        return f"错误: {error_msg}"

# 使用示例
if __name__ == "__main__":
    user_prompt = "请介绍一下量子计算的基本原理"
    response = call_claude(user_prompt)
    print(f"用户问题: {user_prompt}")
    print(f"Claude回答: {response}")



def chat_with_claude():
    """演示与Claude的多轮对话"""
    messages = []
    print("开始与Claude对话（输入'退出'结束）")

    while True:
        user_input = input("你: ")
        if user_input.lower() == "退出":
            break

        # 添加用户消息到对话历史
        messages.append({"role": "user", "content": user_input})

        # 使用新版API调用方式进行多轮对话
        response = anthropic.messages.create(
            model="claude-3-5-sonnet-20241022",
            max_tokens=1000,
            messages=messages
        )

        # 获取AI回复
        ai_response = response.content[0].text
        messages.append({"role": "assistant", "content": ai_response})

        print(f"Claude: {ai_response}")

# 如果需要多轮对话，取消注释下面这行
# chat_with_claude()