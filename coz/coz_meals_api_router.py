import json
from typing import List
from fastapi import HTTPException, Request
from fastapi import APIRouter
from langchain_core.messages import HumanMessage, AIMessage
from pydantic import BaseModel
from starlette.responses import StreamingResponse
from xmltodict import parse
from logger import logger

from coz.meals.meals_agent_service import (
    CozMealsGraphState, MealNameType, cozMealsAgent, get_food_suggestion, SessionManager
)

coz_meals_api_router = APIRouter()


# 定义请求体模型
class ChatRequest(BaseModel):
    message: str = None
    selectedMealNames: List[str] = None


def prepare_user_input(request: ChatRequest, session_manager: 'SessionManager') -> List[HumanMessage]:
    """准备用户输入消息"""
    # 处理用户选择的菜品
    selectedMealNames = request.selectedMealNames
    if selectedMealNames is not None and selectedMealNames != "":
        # 保存用户选择的菜品到会话状态
        session_manager.set_selected_meal_names(selectedMealNames)
        # 创建用户选择菜品的消息
        msg = "user selected meal names: " + ", ".join(selectedMealNames)
        return [HumanMessage(content=msg)]
    else:
        # 普通用户消息
        if request.message is not None:
            return [HumanMessage(content=request.message)]

    return []


@coz_meals_api_router.post("/coz_meals_steam", response_class=StreamingResponse)
def stream(request: ChatRequest, headers_request: Request):
    # 获取请求头的 head 信息
    # 这里假设请求头中包含了 timeZoneId 和 deviceSn
    timezone = headers_request.headers.get("timeZoneId", "")  # 默认时区为 Asia/Shanghai
    deviceSn = headers_request.headers.get("deviceSn", "")  # 默认设备序列号
    session_id = headers_request.headers.get("sessionid", "")
    if timezone == "" or deviceSn == "" or session_id == "":
        logger.error("Missing required headers: timeZoneId or deviceSn or sessionid")
        raise HTTPException(status_code=400, detail="Missing required headers: timeZoneId or deviceSn")
    logger.info(f"coz_meals_receive {deviceSn} request: {request}")

    # 创建会话管理器（会自动加载或初始化会话状态）
    session_manager = SessionManager(deviceSn, session_id, timezone)

    # 创建初始的 graph_state（使用会话中的消息历史）
    graph_state: CozMealsGraphState = session_manager.create_graph_state()

    # 准备用户输入
    user_messages = prepare_user_input(request, session_manager)
    if not user_messages:
        return "error"

    # 判断是否为菜品推荐请求（更精确的关键词匹配）
    recommendation_keywords = ["推荐", "建议", "ideas", "planning", "suggest", "recommend", "给我推荐", "有什么好吃的"]
    arrangement_keywords = ["我想吃", "想要", "安排", "添加", "要吃"]

    # 优先检查是否为安排请求
    is_meal_arrangement = any(keyword in request.message.lower() for keyword in arrangement_keywords)
    # 只有在不是安排请求的情况下，才检查是否为推荐请求
    is_meal_recommendation = not is_meal_arrangement and any(keyword in request.message.lower() for keyword in recommendation_keywords)

    # 将用户消息添加到会话管理器和 graph_state
    session_manager.add_messages(user_messages)
    graph_state["messages"].extend(user_messages)

    async def stream_generator():
        try:
            ai_content = ""
            xml_content = ""
            # 用于存储情况三的mealNames
            mealNames_dict = []
            line_buffer = ""  # 缓存当前行内容，直到遇到换行符

            for chunk, _ in cozMealsAgent.stream(graph_state, stream_mode="messages"):
                if chunk.content is None or chunk.content.strip() == "":
                    continue

                ai_content += chunk.content
                xml_content += chunk.content
                line_buffer += chunk.content

                # 菜品推荐场景已在外部预先判断，这里不需要重新检测

                # 情况一、二、四 - 处理饮食日程信息
                meal_dict = {}
                if "<meal>" in xml_content:
                    xml_content = xml_content[xml_content.index("<meal>") :]
                # 结束标签
                if "</meal>" in xml_content:
                    xml_content = xml_content[: xml_content.index("</meal>") + len("</meal>")]
                    meal_dict = parse(xml_content)
                if meal_dict:
                    # 解析饮食日程信息
                    meal_dict = json.loads(meal_dict.get("meal", ""))

                    # 更新会话管理器中的当前饮食信息
                    session_manager.update_current_meal({
                        "mealDate": meal_dict.get("mealDate", ""),
                        "mealKind": meal_dict.get("mealKind", ""),
                        "mealNames": meal_dict.get("mealNames", [])
                    })

                    # 更新 graph_state 中的当前饮食信息
                    graph_state["current_meal"] = session_manager.get_current_meal()

                    # 处理菜品详细信息
                    current_meal_dict_session = session_manager.get_meal_dict()
                    processed_uids = set()  # 用于去重的集合

                    for mealName in meal_dict.get("mealNames", []):
                        if mealName not in current_meal_dict_session:
                            food_suggestion = get_food_suggestion(
                                foodName=mealName,
                                mealKind=session_manager.get_meal_kind(),
                                mealDate=session_manager.get_meal_date(),
                                deviceSn=deviceSn,
                                session_id=session_id
                            )
                            suggest_meals: MealNameType = food_suggestion["existsMealName"]
                            # print("mealNames: ", suggest_meals)

                            # 对建议的菜品进行去重处理
                            for suggest_meal in suggest_meals:
                                meal_uid = suggest_meal.get("mealUid", "")
                                # 只处理未见过的 mealUid
                                if meal_uid not in processed_uids:
                                    processed_uids.add(meal_uid)

                                    # 更新会话管理器中的菜品信息
                                    current_meal_dict_session[mealName] = {
                                        "img": suggest_meal["img"],
                                        "mealUid": suggest_meal["mealUid"]
                                    }
                                    session_manager.update_meal_dict(current_meal_dict_session)

                                    # 更新 graph_state
                                    graph_state["meal_dict"] = current_meal_dict_session

                                    # 更新菜品名称
                                    current_meal_session = session_manager.get_current_meal()
                                    if mealName in current_meal_session["mealNames"]:
                                        current_meal_session["mealNames"].remove(mealName)
                                    current_meal_session["mealNames"].append(suggest_meal["mealName"])
                                    session_manager.update_current_meal(current_meal_session)
                                    graph_state["current_meal"] = current_meal_session

                                    # 将用户选择的菜品也添加到推荐列表中，避免重复推荐
                                    session_manager.add_recommended_meal_names([suggest_meal["mealName"]])
                                    session_manager.save_state()

                # 情况三 调用工具 get_food_suggestion， 给出菜品建议
                if "<mealNames>" in xml_content:
                    xml_content = xml_content[xml_content.index("<mealNames>") :]
                if "</mealNames>" in xml_content:
                    xml_content = xml_content[: xml_content.index("</mealNames>") + len("</mealNames>")]
                    mealNames_dict = parse(xml_content)

                # 检查是否有完整的行（包含换行符）
                if '\n' in line_buffer:
                    # 分割行，处理每一行
                    lines = line_buffer.split('\n')
                    # 保留最后一个不完整的行
                    line_buffer = lines[-1]

                    # 处理完整的行
                    for line in lines[:-1]:
                        if line.strip():  # 跳过空行
                            # 更严格的过滤条件
                            should_skip = False

                            # 检查XML标签
                            xml_patterns = ["<meal>", "</meal>", "<mealNames>", "</mealNames>"]
                            if any(pattern in line for pattern in xml_patterns):
                                should_skip = True

                            # 检查JSON结构
                            json_patterns = ['"mealDate"', '"mealKind"', '"mealNames"', '"鸡肉"']
                            if any(pattern in line for pattern in json_patterns):
                                should_skip = True

                            # 检查括号和大括号
                            stripped_line = line.strip()
                            if (stripped_line.startswith('{') or stripped_line.endswith('}') or
                                stripped_line.startswith('[') or stripped_line.endswith(']') or
                                stripped_line.startswith('"') and stripped_line.endswith('"') or
                                stripped_line == ',' or stripped_line.endswith(',')):
                                should_skip = True

                            # 如果行只包含引号内容（如 "鸡肉"），也跳过
                            if stripped_line.startswith('"') and stripped_line.endswith('"') and len(stripped_line) > 2:
                                should_skip = True

                            if should_skip:
                                continue

                            # 处理自然语言输出
                            if is_meal_recommendation:
                                # 菜品推荐场景：完全不显示自然语言
                                pass
                            else:
                                # 发送过滤后的行
                                messageData = {"responseDataClassify": "showChatMessage", "responseData": line + '\n'}
                                chat_event = {"data": json.dumps(messageData, ensure_ascii=False)}
                                yield f"data: {chat_event['data']}\n\n"

            # 情况三 调用工具 get_food_suggestion ，返回工具执行结果
            if mealNames_dict and isinstance(mealNames_dict, dict):
                # 如果AI返回的 mealNames 为新的，就调用工具 get_food_suggestion ，替换为新菜品
                print(f"AI返回的mealNames: {mealNames_dict['mealNames']}")  # 调试信息
                new_mealNames = []
                session_meal_dict = session_manager.get_meal_dict()

                for mealName in mealNames_dict["mealNames"]:
                    if mealName not in session_meal_dict:
                        food_suggestion = get_food_suggestion(
                            foodName=mealName,
                            mealKind=session_manager.get_meal_kind(),
                            mealDate=session_manager.get_meal_date(),
                            deviceSn=deviceSn,
                            session_id=session_id
                        )
                        mealNames: MealNameType = food_suggestion["existsMealName"]
                        # print("mealNames: ", mealNames)
                        new_mealNames.extend(mealNames)
                    else:
                        # 如果已存在，直接使用会话中的信息
                        meal_info = session_meal_dict[mealName]
                        new_mealNames.append({
                            "mealName": mealName,
                            "img": meal_info.get("img", ""),
                            "mealUid": meal_info.get("mealUid", "")
                        })

                # 格式化返回数据并去重（基于 mealUid）
                formatted_meals = []
                seen_uids = set()
                for meal in new_mealNames:
                    if isinstance(meal, dict) and "mealName" in meal:
                        meal_uid = meal.get("mealUid", "")
                        # 只添加未见过的 mealUid
                        if meal_uid not in seen_uids:
                            formatted_meals.append(meal)
                            seen_uids.add(meal_uid)
                    else:
                        # 处理字符串类型的菜品名
                        meal_name = meal if isinstance(meal, str) else str(meal)
                        formatted_meals.append({
                            "mealName": meal_name,
                            "img": session_meal_dict.get(meal_name, {}).get("img", ""),
                            "mealUid": session_meal_dict.get(meal_name, {}).get("mealUid", "")
                        })

                # 将推荐的菜品名称添加到 recommendedMealNames 列表中，避免重复推荐
                recommended_meal_names = [meal.get("mealName", "") for meal in formatted_meals if meal.get("mealName")]
                if recommended_meal_names:
                    session_manager.add_recommended_meal_names(recommended_meal_names)
                    session_manager.save_state()

                # 将工具调用的内容转换为字典格式
                cardData = {"responseDataClassify": "food_suggestion", "responseData": formatted_meals}
                # 将工具调用的字典内容作为SSE事件发送
                confirm_event = {"data": json.dumps(cardData, ensure_ascii=False)}
                yield f"data: {confirm_event['data']}\n\n"

        finally:
            # 处理剩余的缓存内容
            if line_buffer.strip():
                # 更严格的过滤条件
                should_skip = False

                # 检查XML标签
                xml_patterns = ["<meal>", "</meal>", "<mealNames>", "</mealNames>"]
                if any(pattern in line_buffer for pattern in xml_patterns):
                    should_skip = True

                # 检查JSON结构
                json_patterns = ['"mealDate"', '"mealKind"', '"mealNames"', '"鸡肉"']
                if any(pattern in line_buffer for pattern in json_patterns):
                    should_skip = True

                # 检查括号和大括号
                stripped_buffer = line_buffer.strip()
                if (stripped_buffer.startswith('{') or stripped_buffer.endswith('}') or
                    stripped_buffer.startswith('[') or stripped_buffer.endswith(']') or
                    stripped_buffer.startswith('"') and stripped_buffer.endswith('"') or
                    stripped_buffer == ',' or stripped_buffer.endswith(',')):
                    should_skip = True

                if not should_skip and not is_meal_recommendation:
                    # 发送剩余内容
                    messageData = {"responseDataClassify": "showChatMessage", "responseData": line_buffer}
                    chat_event = {"data": json.dumps(messageData, ensure_ascii=False)}
                    yield f"data: {chat_event['data']}\n\n"

            # 保存最终的AI响应消息到会话管理器
            logger.info(f"\n[Session: {session_id}] Stream finished. Saving complete history.")
            ai_message = AIMessage(content=ai_content)
            session_manager.add_message(ai_message)

            # 打印最近的消息用于调试
            recent_messages = session_manager.get_messages()[-2:]
            for msg in recent_messages:
                msg.pretty_print()

            # 保存会话状态（包括消息历史和所有会话数据）
            session_manager.save_state()

            # 清理工具调用历史
            session_manager.clear_tool_history()

    return StreamingResponse(stream_generator(), media_type="text/event-stream")
