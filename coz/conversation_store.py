# This will store simple lists of dictionaries, not complex objects
from typing import Dict, List, TypedDict
from langchain_core.messages import BaseMessage


class CurrentMealType(TypedDict):
    """当前饮食信息的类型定义"""
    mealDate: str      # 饮食日期，格式：YYYY-MM-DD
    mealKind: str      # 饮食类型：breakfast/lunch/dinner/snack
    mealNames: List[str]  # 菜品名称列表


class SessionStateType(TypedDict):
    """会话状态的类型定义

    存储单个会话的完整状态信息，包括：
    - 消息历史记录
    - 当前饮食安排信息
    - 菜品详细信息缓存
    - 工具调用历史
    - 用户选择记录
    """
    messages: List[BaseMessage]           # 会话消息历史（LangChain BaseMessage对象）
    current_meal: CurrentMealType         # 当前饮食日程信息
    meal_dict: Dict[str, dict]           # 菜品详细信息缓存 {菜品名: {img, mealUid, ...}}
    recommendedMealNames: List[str]  # AI推荐过的所有菜品名称列表

    last_tool_call: str                  # 最后一次工具调用名称
    selected_meal_names: List[str]        # 用户选择的菜品名称列表
    tool_history: List[str]               # 工具调用历史记录


# ==================== 废弃警告 ====================
# 以下全局变量正在逐步迁移到 SessionManager 中管理
# 建议使用 SessionManager 来管理所有会话相关的状态
# ================================================

# 会话历史存储 - 存储 BaseMessage 对象 [废弃中，使用 SessionManager.get_messages()]
# meals_conversation_histories: Dict[str, List[BaseMessage]] = {}

# 成员对话历史 - 存储字典对象 [保留，非饮食相关]
member_conversation_histories: Dict[str, List[dict]] = {}

# 会话状态存储 - 存储每个会话的完整状态信息 [主要存储方式]
# 键格式："{deviceSn}_meals_{session_id}"
# 值：SessionStateType 包含完整的会话状态
session_states: Dict[str, SessionStateType] = {}
